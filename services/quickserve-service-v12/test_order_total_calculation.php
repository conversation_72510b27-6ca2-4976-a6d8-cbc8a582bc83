<?php

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Order Total Calculation Test ===" . PHP_EOL;
echo "Testing both normal orders and express delivery charges" . PHP_EOL . PHP_EOL;

try {
    // Test scenario 1: Normal 5-day lunch subscription
    echo "Scenario 1: Normal 5-day lunch subscription" . PHP_EOL;
    echo "- Meal: ₹75/day × 5 days = ₹375" . PHP_EOL;
    echo "- Tax (5%): ₹18.75" . PHP_EOL;
    echo "- Expected Total: ₹393.75" . PHP_EOL . PHP_EOL;

    // Test scenario 2: Express 5-day lunch subscription (current day included)
    echo "Scenario 2: Express 5-day lunch subscription (current day included)" . PHP_EOL;
    echo "- Meal: ₹75/day × 5 days = ₹375" . PHP_EOL;
    echo "- Tax on meals (5%): ₹18.75" . PHP_EOL;
    echo "- Express charge: ₹50 (today only)" . PHP_EOL;
    echo "- Tax on express (5%): ₹2.50" . PHP_EOL;
    echo "- Expected Total: ₹446.25" . PHP_EOL . PHP_EOL;

    // Test scenario 3: Express 5-day lunch subscription (current day NOT included)
    echo "Scenario 3: Express 5-day lunch subscription (current day NOT included)" . PHP_EOL;
    echo "- Meal: ₹75/day × 5 days = ₹375" . PHP_EOL;
    echo "- Tax on meals (5%): ₹18.75" . PHP_EOL;
    echo "- Express charge: ₹0 (today not in delivery days)" . PHP_EOL;
    echo "- Expected Total: ₹393.75" . PHP_EOL . PHP_EOL;

    // Check current tax settings
    echo "Current Tax Settings:" . PHP_EOL;
    $taxSettings = DB::table('settings')
        ->whereIn('key', ['GLOBAL_TAX_RATE', 'GLOBAL_TAX_APPLICABLE'])
        ->get();

    foreach ($taxSettings as $setting) {
        echo "- {$setting->key}: {$setting->value}" . PHP_EOL;
    }
    echo PHP_EOL;

    // Check express delivery charge settings
    echo "Express Delivery Charge Settings:" . PHP_EOL;
    $expressSettings = DB::table('settings')
        ->where('key', 'LIKE', '%EXPRESS_EXTRA_DELIVERY_CHARGE%')
        ->get();

    foreach ($expressSettings as $setting) {
        echo "- {$setting->key}: {$setting->value}" . PHP_EOL;
    }
    echo PHP_EOL;

    // Test the calculation logic manually
    echo "Manual Calculation Test:" . PHP_EOL;
    
    // Simulate meal data
    $mealAmount = 75.00; // ₹75 per day
    $deliveryDays = 5;
    $taxRate = 5.0; // 5%
    $expressCharge = 50.00; // ₹50 express charge
    
    // Normal calculation
    $baseMealTotal = $mealAmount * $deliveryDays; // ₹375
    $baseTax = round($baseMealTotal * $taxRate / 100, 2); // ₹18.75
    $normalTotal = $baseMealTotal + $baseTax; // ₹393.75
    
    echo "Normal Order:" . PHP_EOL;
    echo "- Base meal total: ₹{$baseMealTotal}" . PHP_EOL;
    echo "- Tax: ₹{$baseTax}" . PHP_EOL;
    echo "- Total: ₹{$normalTotal}" . PHP_EOL . PHP_EOL;
    
    // Express calculation (current day included)
    $expressTax = round($expressCharge * $taxRate / 100, 2); // ₹2.50
    $expressTotal = $baseMealTotal + $baseTax + $expressCharge + $expressTax; // ₹446.25
    
    echo "Express Order (current day included):" . PHP_EOL;
    echo "- Base meal total: ₹{$baseMealTotal}" . PHP_EOL;
    echo "- Tax on meals: ₹{$baseTax}" . PHP_EOL;
    echo "- Express charge: ₹{$expressCharge}" . PHP_EOL;
    echo "- Tax on express: ₹{$expressTax}" . PHP_EOL;
    echo "- Total: ₹{$expressTotal}" . PHP_EOL . PHP_EOL;

    // Test with multiple meal types
    echo "Multiple Meal Types Test:" . PHP_EOL;
    echo "Breakfast (₹50/day × 5 days) + Lunch (₹75/day × 5 days)" . PHP_EOL;
    
    $breakfastAmount = 50.00;
    $lunchAmount = 75.00;
    
    $breakfastTotal = $breakfastAmount * $deliveryDays; // ₹250
    $lunchTotal = $lunchAmount * $deliveryDays; // ₹375
    $combinedMealTotal = $breakfastTotal + $lunchTotal; // ₹625
    $combinedTax = round($combinedMealTotal * $taxRate / 100, 2); // ₹31.25
    $combinedTotal = $combinedMealTotal + $combinedTax; // ₹656.25
    
    echo "- Breakfast total: ₹{$breakfastTotal}" . PHP_EOL;
    echo "- Lunch total: ₹{$lunchTotal}" . PHP_EOL;
    echo "- Combined meal total: ₹{$combinedMealTotal}" . PHP_EOL;
    echo "- Tax: ₹{$combinedTax}" . PHP_EOL;
    echo "- Total: ₹{$combinedTotal}" . PHP_EOL . PHP_EOL;

    echo "Test completed successfully!" . PHP_EOL;

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . PHP_EOL;
    echo "Trace: " . $e->getTraceAsString() . PHP_EOL;
}
